---
id: agents-handbook
title: "AGENTS.md — PaceySpace Agent Handbook (Template)"
description: "Template agent guide for PaceySpace: tooling, workflows, safety gates, CI/CD, Docker+OrbStack, Python uv, storage rules, visual aids, task creation to Obsidian/Dataview, and strict metadata policies."
company: "PaceySpace"
author: "Jordan Pacey"
owner: "<EMAIL>"
version: "1.0.3"
created: "2025-09-03"
updated: "2025-09-03"
status: "active"
type: "handbook/template"
project: "template"
area: "engineering/devops"
agents_supported: ["Cline", "Roo Code", "Augment Code"]
os: "macOS"
shell: "bash"
container_runtime: "OrbStack (docker CLI)"
languages: ["C#", ".NET 8", "JavaScript", "Python"]
db: "MariaDB 10.11"
storage: ["S3-compatible", "Backblaze B2 (S3 endpoints)"]
hosting: ["nginx", "cloud VM (e.g., AWS EC2)"]
ci_cd: true
requires_metadata: true
visual_aids: true
diagram_formats: ["Mermaid", "SVG", "PNG"]
task_management: true
obsidian:
  dataview: true
  tasks_plugin: true
  mermaid: true
  tags: ["agents","preferences","dev-tools","workflow","ci-cd","security","s3","b2","nginx","docker","orbstack","dotnet","mariadb","testing","cost-optimisation","obsidian","cline","roocode","augmentcode","python","uv","pip","mermaid","svg","diagrams","visual-aids","tasks","dataview"]
aliases: ["AGENTS","Agent Handbook","PaceySpace Agents Template"]
tags:
  - agents
  - preferences
  - dev-tools
  - workflow
  - ci-cd
  - security
  - s3
  - b2
  - nginx
  - docker
  - orbstack
  - dotnet
  - mariadb
  - testing
  - cost-optimisation
  - obsidian
  - cline
  - roocode
  - augmentcode
  - python
  - uv
  - pip
  - mermaid
  - svg
  - diagrams
  - visual-aids
  - tasks
  - dataview
permissions:
  safe_runs: ["unit_tests","linters","type_checks","builds","--help smokes"]
  dangerous_runs: ["db_migrations","deployments","long_jobs","external_cost_calls","data_writes"]
  ask_before:
    - "commits_pushes"
    - "installing_upgrading_dependencies"
    - "deployments_or_migrations"
    - "destructive_or_costly_operations"
secrets_policy:
  never_log: true
  use_env_or_secret_manager: true
  mask_in_outputs: true
profile:
  region: "Australia (Brisbane focus)"
  cost_sensitive: true
  nginx_frontend_preferred: true
  fallback_paths_required: true
  object_store_metadata_priority: true
---

## Purpose
- This file is optimized for Large Language Model Agents (Cline, Roo Code, Augment Code).
- It defines how to work in PaceySpace environments: tools, safety gates, CI/CD, data rules, documentation standards, visual aids, and task creation workflows.
- I require accurate and comprehensive Markdown file metadata in the header to power Obsidian Dataview, vault search workflows, and archival retrieval.

## My Profile and Priorities
- Location/clients: Brisbane, Australia; prefer low-latency for AU/NZ users
- Cost sensitivity: Prefer cost-effective hosting; avoid unnecessary spend
- Reliability: Maintain fallback paths and reversible operations during transitions
- Frontend hosting: Prefer nginx as a fronting server
- Data displays: When domain data includes object-store metadata (e.g., S3), prefer that metadata over fallback fields
- Documentation: I save agent outputs to Obsidian; I want clean, skimmable Markdown with tags and proper metadata

## Stack Overview
| Area | Default/Preference | Tags |
|---|---|---|
| Frontend hosting | nginx (alpine), static assets, /health | #nginx |
| Backend | .NET 8 (ASP.NET Core), EF Core | #dotnet |
| Database | MariaDB 10.11 (SQLite okay for local/dev) | #mariadb |
| Object storage | S3-compatible, often Backblaze B2 endpoints/CDN | #s3 #b2 |
| Containers | Docker CLI with OrbStack (NOT Docker Desktop) on macOS | #docker #orbstack |
| OS & shell | macOS + bash | #macos #bash |
| CI/CD | Maintain fallback deployment options; secure credential prompts | #ci-cd |
| Python | Use uv for Python version/env mgmt and installs | #python #uv #pip |

## Tools & Services I Use
- OrbStack (Docker runtime on macOS) — preferred over Docker Desktop
- Docker, docker-compose
- nginx (alpine)
- .NET 8 SDK/Runtime + EF Core tooling
- MariaDB 10.11
- S3-compatible APIs (Backblaze B2 service URLs/CDN common)
- GitHub (SSH)
- Python with uv (venv, version mgmt, fast installer)

## Package Management Policy
- Always use package managers; do not hand-edit lockfiles except for non-version config:
  - Node: npm/yarn/pnpm (match repo; default npm)
  - Python: uv preferred (env + installs + lock), pip usage routed via uv
  - Rust: cargo add/remove
  - Go: go get; go mod tidy
  - Ruby: bundler (bundle add/remove)
  - PHP: composer
  - C#/.NET: dotnet add/remove package
  - Java: Gradle/Maven
- Python with uv (examples):
```bash
uv python install 3.11
uv venv
uv pip install -r requirements.txt    # or: uv add <pkg>
uv run pytest -q
```

## Security, Permissions, and Safety Gates
- Always ask before:
  - Committing/pushing code
  - Installing/upgrading/removing dependencies
  - Deployments or database migrations
  - Running destructive or high-cost operations
- Credentials/secrets:
  - Use env vars or secrets manager; never log or commit secrets
  - Mask secrets in outputs; request explicitly and securely when needed
- Cost/safety:
  - Prefer smallest, least-expensive steps; get approval for long-running/heavy jobs

## Execution & Validation Runs
- When asked to "make sure it runs/works/builds/tests", actually run minimal safe commands and report:
  - Commands + cwd
  - Exit codes
  - Key output lines
- Safe-by-default runs (no approval needed):
  - Unit/integration tests, linters, type-checkers, builds, short CLI "--help"
- Ask first:
  - Data migrations, deployments, external calls that incur spend, heavy jobs, data writes

## CI/CD Expectations
- Maintain fallback deployment options during transitions
- Secure credential prompts: name variables/scopes; explain why needed
- Don't push or PR without confirming branch and target conventions
- After pushes: verify CI status before requesting review

## Data Handling & Storage
- Display rule: Prefer object-store metadata (S3) when available; fallback only when missing
- S3/B2:
  - Direct or CDN-backed S3 public URLs common (Backblaze B2 endpoints)
  - Keep DB and object metadata consistent
- Validation:
  - Check S3 object existence when referencing keys
  - For B2 sync, verify bucket/key/url metadata completeness

## Docker, Compose, and OrbStack Usage
- Runtime: OrbStack on macOS (assume docker CLI; avoid Docker Desktop-only features)
- Dev UX:
  - Provide compose targets that allow frontend-only serving (nginx + static files) without upstream API/uploader
  - Ensure /health endpoints for services
- Production UX:
  - Pull private images only with proper auth; otherwise provide local build fallback
- Logs:
  - Avoid logging secrets; present key lines; bound output sizes

## nginx Preferences
- Minimal configs with /health
- Proxy to backend only when backend is up/healthy
- For dev, allow static frontend serving without upstreams
- Controls/navigation visible and usable on mobile; keep within container bounds

## Code Quality, Testing, and Standards
- Testing:
  - Encourage unit/integration tests; iterate until green
  - Document how to run tests; keep runs minimal
- Coding standards:
  - Qualify namespaces to avoid type collisions (e.g., common names like MigrationResult)
  - Clear error handling and logging
- Docs:
  - Concise and skimmable; minimal necessary code excerpts

## Visual Aids Policy
- Use visual aids whenever suitable to clarify architecture, flows, or data models.
- Preferred and Obsidian-compatible formats:
  - Mermaid diagrams (native in Obsidian; ideal for architecture, sequence, flowcharts)
  - SVG (scalable, crisp; widely supported by Obsidian and plugins)
  - PNG (fallback for screenshots or raster visuals)
- Guidance:
  - Prefer Mermaid for diagrams that benefit from version control and easy diffing.
  - Provide a short caption/title and purpose.
  - Keep diagrams close to the relevant section and include tags for retrieval.
  - For complex visuals, include both a Mermaid source and an exported SVG/PNG if useful.

Example Mermaid (architecture overview):
```mermaid
flowchart LR
  Browser -->|HTTPS| Nginx
  Nginx -->|/api| API
  Nginx -->|/| Frontend
  API --> DB[(MariaDB)]
  API --> S3[(S3/B2)]
```

## Task Creation and Management Policy
- Create follow-up tasks for next steps, action items, and future work.
- Tasks are managed by Obsidian Dataview and Tasks plugin.

### Task Saving Workflow
1. First preference: Save tasks directly to my Obsidian vault (if accessible)
2. Second preference: Prompt the user to share the vault location; then save there
3. Fallback: Save a task document to docs/ in the project root

Agent prompt to user (example):
```text
Please provide your Obsidian vault path (absolute path). If unavailable, I will save tasks to docs/ at project root.
```

### Task Format Requirements
- Use Obsidian task syntax with metadata frontmatter compatible with Dataview:
```markdown
---
id: task-YYYYMMDD-HHMMSS
title: "Task Title"
created: "YYYY-MM-DD"
due: "YYYY-MM-DD"        # optional
priority: "high|medium|low"
project: "template"      # or set per-project when known
area: "engineering"
status: "todo"           # todo|in-progress|done|cancelled
tags: ["task","follow-up","engineering"]
---

# Task Title

## Description
Brief description of what needs to be done.

## Acceptance Criteria
- [ ] Specific deliverable 1
- [ ] Specific deliverable 2
- [ ] Specific deliverable 3

## Context
Background information, links to related work, etc.

## Notes
Additional notes, considerations, or dependencies.
```

### Task Creation Triggers
- When completing work that requires follow-up
- When identifying technical debt or improvements
- When encountering blockers that need resolution
- When planning multi-step implementations
- When the user requests task creation explicitly

### Task Metadata Standards
- Always include comprehensive frontmatter for Dataview queries
- Use consistent tag taxonomy: #task, #area, #priority
- Include due dates for time-sensitive items
- Link to related files, PRs, or documentation
- Use clear, actionable titles

## Obsidian Formatting Rules for Agent Outputs
- Use clean Markdown with:
  - Headings (##/###), concise paragraphs, bullet lists
  - Horizontal rules (---) at top and bottom of documents I’ll save
  - Code blocks with language hints (```bash
,
```csharp, ```yaml
)
  - Tags (e.g., #s3, #nginx, #optimisations)
  - Visual aids as per the Visual Aids Policy (Mermaid/SVG/PNG)
  - Task creation as per Task Creation Policy when applicable
- Augment Code:
  - When quoting existing repository code, wrap in:
    - <augment_code_snippet path="relative/path" mode="EXCERPT">
…
</augment_code_snippet>
  - Use four backticks inside; keep each excerpt under 10 lines

## Secrets & Environment Variables (Common Patterns)
```env
# Database
DB_ROOT_PASSWORD=
DB_USER=
DB_PASSWORD=
DB_NAME=AppDb

# API/JWT
JWT_SECRET=

# S3 / B2 (S3-compatible)
S3_BUCKET_NAME=
S3_ACCESS_KEY=
S3_SECRET_KEY=
S3_REGION=
S3_ENDPOINT=

# Backblaze B2 (when used)
B2_APPLICATION_KEY_ID=
B2_APPLICATION_KEY=
B2_BUCKET_ID=
```
- Example connection string:
```env
ConnectionStrings__DefaultConnection=Server=db;Database=${DB_NAME};User=${DB_USER};Password=${DB_PASSWORD};Port=3306;
```
- Rule: Never log or commit secrets.

## Agent Behavior Model (Machine-Readable Summary)
- Information-gathering:
  - Targeted, minimal; stop once sufficient; confirm signatures before edits
- Task management:
  - Use a short Investigate/Triage task for non-trivial work
  - Keep one task IN_PROGRESS; batch state updates
  - Create follow-up tasks as per Task Creation Policy
- Edits:
  - Conservative, minimal; respect repository structure and conventions
- Efficiency:
  - Prefer smallest set of high-signal actions and validations; ask if blocked
- Communication:
  - Summarize intentions and next steps; ask before risky actions
  - Create tasks for identified follow-up work

## Hosting & Cost Optimization
- Hosting: cloud VM providers (e.g., AWS EC2); consider Vultr/Hetzner/OVH for cost savings
- Region: Prefer AU to serve Brisbane clients
- CDN: Use when cost/performance beneficial; S3-compatible endpoints common
- Avoid expensive operations; reuse caches/artifacts; prune unused images/containers

## Agent Compatibility Notes
- Cline:
  - Follow this doc; propose minimal command sets; show commands and expected outputs
  - Create tasks for follow-up work as per Task Creation Policy
- Roo Code:
  - Short investigations; confirm signatures; tight edits; ask before push/deploy/package changes
  - Generate tasks for identified next steps
- Augment Code:
  - Use codebase context tools judiciously
  - When showing code, use <augment_code_snippet> tags
  - Follow tasklist triggers and minimal tool-call ethos
  - Create follow-up tasks when work identifies future needs

## Ready-to-Use Command Snippets

- Frontend (dev) with OrbStack:
```bash
docker compose -f deploy/docker-compose.dev.yml up -d frontend
curl -s -o /dev/null -w "%{http_code}\n" http://localhost:8080/index.html
```
- Health checks:
```bash
curl -s http://localhost:8080/health
```
- .NET backend quick build:
```bash
cd path/to/Your.Api
dotnet restore && dotnet build -c Release
```
- Python with uv:
```bash
uv python install 3.11
uv venv
uv pip install -r requirements.txt   # or: uv add <pkg>
uv run pytest -q
```
- MariaDB connectivity:
```bash
mysqladmin ping -h 127.0.0.1 -u root -p"${DB_ROOT_PASSWORD}"
```

---

### Tags
#agents #preferences #dev-tools #workflow #ci-cd #security #s3 #b2 #nginx #docker #orbstack #dotnet #mariadb #testing #cost-optimisation #obsidian #cline #roocode #augmentcode #python #uv #pip #mermaid #svg #diagrams #visual-aids #tasks #dataview

---
---
id: agents-handbook
title: "AGENTS.md — PaceySpace Agent Handbook (Template)"
description: "Template agent guide for PaceySpace: tooling, workflows, safety gates, CI/CD, Docker+OrbStack, Python uv, storage rules, visual aids, task creation to Obsidian/Dataview, and strict metadata policies."
company: "PaceySpace"
author: "Jordan Pacey"
owner: "<EMAIL>"
version: "1.0.3"
created: "2025-09-03"
updated: "2025-09-03"
status: "active"
type: "handbook/template"
project: "template"
area: "engineering/devops"
agents_supported: ["Cline", "Roo Code", "Augment Code"]
os: "macOS"
shell: "bash"
container_runtime: "OrbStack (docker CLI)"
languages: ["C#", ".NET 8", "JavaScript", "Python"]
db: "MariaDB 10.11"
storage: ["S3-compatible", "Backblaze B2 (S3 endpoints)"]
hosting: ["nginx", "cloud VM (e.g., AWS EC2)"]
ci_cd: true
requires_metadata: true
visual_aids: true
diagram_formats: ["Mermaid", "SVG", "PNG"]
task_management: true
obsidian:
  dataview: true
  tasks_plugin: true
  mermaid: true
  tags: ["agents","preferences","dev-tools","workflow","ci-cd","security","s3","b2","nginx","docker","orbstack","dotnet","mariadb","testing","cost-optimisation","obsidian","cline","roocode","augmentcode","python","uv","pip","mermaid","svg","diagrams","visual-aids","tasks","dataview"]
aliases: ["AGENTS","Agent Handbook","PaceySpace Agents Template"]
tags:
  - agents
  - preferences
  - dev-tools
  - workflow
  - ci-cd
  - security
  - s3
  - b2
  - nginx
  - docker
  - orbstack
  - dotnet
  - mariadb
  - testing
  - cost-optimisation
  - obsidian
  - cline
  - roocode
  - augmentcode
  - python
  - uv
  - pip
  - mermaid
  - svg
  - diagrams
  - visual-aids
  - tasks
  - dataview
permissions:
  safe_runs: ["unit_tests","linters","type_checks","builds","--help smokes"]
  dangerous_runs: ["db_migrations","deployments","long_jobs","external_cost_calls","data_writes"]
  ask_before:
    - "commits_pushes"
    - "installing_upgrading_dependencies"
    - "deployments_or_migrations"
    - "destructive_or_costly_operations"
secrets_policy:
  never_log: true
  use_env_or_secret_manager: true
  mask_in_outputs: true
profile:
  region: "Australia (Brisbane focus)"
  cost_sensitive: true
  nginx_frontend_preferred: true
  fallback_paths_required: true
  object_store_metadata_priority: true
---

## Purpose
- This file is optimized for Large Language Model Agents (Cline, Roo Code, Augment Code).
- It defines how to work in PaceySpace environments: tools, safety gates, CI/CD, data rules, documentation standards, visual aids, and task creation workflows.
- I require accurate and comprehensive Markdown file metadata in the header to power Obsidian Dataview, vault search workflows, and archival retrieval.

## My Profile and Priorities
- Location/clients: Brisbane, Australia; prefer low-latency for AU/NZ users
- Cost sensitivity: Prefer cost-effective hosting; avoid unnecessary spend
- Reliability: Maintain fallback paths and reversible operations during transitions
- Frontend hosting: Prefer nginx as a fronting server
- Data displays: When domain data includes object-store metadata (e.g., S3), prefer that metadata over fallback fields
- Documentation: I save agent outputs to Obsidian; I want clean, skimmable Markdown with tags and proper metadata

## Stack Overview
| Area | Default/Preference | Tags |
|---|---|---|
| Frontend hosting | nginx (alpine), static assets, /health | #nginx |
| Backend | .NET 8 (ASP.NET Core), EF Core | #dotnet |
| Database | MariaDB 10.11 (SQLite okay for local/dev) | #mariadb |
| Object storage | S3-compatible, often Backblaze B2 endpoints/CDN | #s3 #b2 |
| Containers | Docker CLI with OrbStack (NOT Docker Desktop) on macOS | #docker #orbstack |
| OS & shell | macOS + bash | #macos #bash |
| CI/CD | Maintain fallback deployment options; secure credential prompts | #ci-cd |
| Python | Use uv for Python version/env mgmt and installs | #python #uv #pip |

## Tools & Services I Use
- OrbStack (Docker runtime on macOS) — preferred over Docker Desktop
- Docker, docker-compose
- nginx (alpine)
- .NET 8 SDK/Runtime + EF Core tooling
- MariaDB 10.11
- S3-compatible APIs (Backblaze B2 service URLs/CDN common)
- GitHub (SSH)
- Python with uv (venv, version mgmt, fast installer)

## Package Management Policy
- Always use package managers; do not hand-edit lockfiles except for non-version config:
  - Node: npm/yarn/pnpm (match repo; default npm)
  - Python: uv preferred (env + installs + lock), pip usage routed via uv
  - Rust: cargo add/remove
  - Go: go get; go mod tidy
  - Ruby: bundler (bundle add/remove)
  - PHP: composer
  - C#/.NET: dotnet add/remove package
  - Java: Gradle/Maven
- Python with uv (examples):
```bash
uv python install 3.11
uv venv
uv pip install -r requirements.txt    # or: uv add <pkg>
uv run pytest -q
```

## Security, Permissions, and Safety Gates
- Always ask before:
  - Committing/pushing code
  - Installing/upgrading/removing dependencies
  - Deployments or database migrations
  - Running destructive or high-cost operations
- Credentials/secrets:
  - Use env vars or secrets manager; never log or commit secrets
  - Mask secrets in outputs; request explicitly and securely when needed
- Cost/safety:
  - Prefer smallest, least-expensive steps; get approval for long-running/heavy jobs

## Execution & Validation Runs
- When asked to "make sure it runs/works/builds/tests", actually run minimal safe commands and report:
  - Commands + cwd
  - Exit codes
  - Key output lines
- Safe-by-default runs (no approval needed):
  - Unit/integration tests, linters, type-checkers, builds, short CLI "--help"
- Ask first:
  - Data migrations, deployments, external calls that incur spend, heavy jobs, data writes

## CI/CD Expectations
- Maintain fallback deployment options during transitions
- Secure credential prompts: name variables/scopes; explain why needed
- Don't push or PR without confirming branch and target conventions
- After pushes: verify CI status before requesting review

## Data Handling & Storage
- Display rule: Prefer object-store metadata (S3) when available; fallback only when missing
- S3/B2:
  - Direct or CDN-backed S3 public URLs common (Backblaze B2 endpoints)
  - Keep DB and object metadata consistent
- Validation:
  - Check S3 object existence when referencing keys
  - For B2 sync, verify bucket/key/url metadata completeness

## Docker, Compose, and OrbStack Usage
- Runtime: OrbStack on macOS (assume docker CLI; avoid Docker Desktop-only features)
- Dev UX:
  - Provide compose targets that allow frontend-only serving (nginx + static files) without upstream API/uploader
  - Ensure /health endpoints for services
- Production UX:
  - Pull private images only with proper auth; otherwise provide local build fallback
- Logs:
  - Avoid logging secrets; present key lines; bound output sizes

## nginx Preferences
- Minimal configs with /health
- Proxy to backend only when backend is up/healthy
- For dev, allow static frontend serving without upstreams
- Controls/navigation visible and usable on mobile; keep within container bounds

## Code Quality, Testing, and Standards
- Testing:
  - Encourage unit/integration tests; iterate until green
  - Document how to run tests; keep runs minimal
- Coding standards:
  - Qualify namespaces to avoid type collisions (e.g., common names like MigrationResult)
  - Clear error handling and logging
- Docs:
  - Concise and skimmable; minimal necessary code excerpts

## Visual Aids Policy
- Use visual aids whenever suitable to clarify architecture, flows, or data models.
- Preferred and Obsidian-compatible formats:
  - Mermaid diagrams (native in Obsidian; ideal for architecture, sequence, flowcharts)
  - SVG (scalable, crisp; widely supported by Obsidian and plugins)
  - PNG (fallback for screenshots or raster visuals)
- Guidance:
  - Prefer Mermaid for diagrams that benefit from version control and easy diffing.
  - Provide a short caption/title and purpose.
  - Keep diagrams close to the relevant section and include tags for retrieval.
  - For complex visuals, include both a Mermaid source and an exported SVG/PNG if useful.

Example Mermaid (architecture overview):
```mermaid
flowchart LR
  Browser -->|HTTPS| Nginx
  Nginx -->|/api| API
  Nginx -->|/| Frontend
  API --> DB[(MariaDB)]
  API --> S3[(S3/B2)]
```

## Task Creation and Management Policy
- Create follow-up tasks for next steps, action items, and future work.
- Tasks are managed by Obsidian Dataview and Tasks plugin.

### Task Saving Workflow
1. First preference: Save tasks directly to my Obsidian vault (if accessible)
2. Second preference: Prompt the user to share the vault location; then save there
3. Fallback: Save a task document to docs/ in the project root

Agent prompt to user (example):
```text
Please provide your Obsidian vault path (absolute path). If unavailable, I will save tasks to docs/ at project root.
```

### Task Format Requirements
- Use Obsidian task syntax with metadata frontmatter compatible with Dataview:
```markdown
---
id: task-YYYYMMDD-HHMMSS
title: "Task Title"
created: "YYYY-MM-DD"
due: "YYYY-MM-DD"        # optional
priority: "high|medium|low"
project: "template"      # or set per-project when known
area: "engineering"
status: "todo"           # todo|in-progress|done|cancelled
tags: ["task","follow-up","engineering"]
---

# Task Title

## Description
Brief description of what needs to be done.

## Acceptance Criteria
- [ ] Specific deliverable 1
- [ ] Specific deliverable 2
- [ ] Specific deliverable 3

## Context
Background information, links to related work, etc.

## Notes
Additional notes, considerations, or dependencies.
```

### Task Creation Triggers
- When completing work that requires follow-up
- When identifying technical debt or improvements
- When encountering blockers that need resolution
- When planning multi-step implementations
- When the user requests task creation explicitly

### Task Metadata Standards
- Always include comprehensive frontmatter for Dataview queries
- Use consistent tag taxonomy: #task, #area, #priority
- Include due dates for time-sensitive items
- Link to related files, PRs, or documentation
- Use clear, actionable titles

## Obsidian Formatting Rules for Agent Outputs
- Use clean Markdown with:
  - Headings (##/###), concise paragraphs, bullet lists
  - Horizontal rules (---) at top and bottom of documents I’ll save
  - Code blocks with language hints (```bash, ```csharp, ```yaml)
  - Tags (e.g., #s3, #nginx, #optimisations)
  - Visual aids as per the Visual Aids Policy (Mermaid/SVG/PNG)
  - Task creation as per Task Creation Policy when applicable
- Augment Code:
  - When quoting existing repository code, wrap in:
    - <augment_code_snippet path="relative/path" mode="EXCERPT"> … </augment_code_snippet>
  - Use four backticks inside; keep each excerpt under 10 lines

## Secrets & Environment Variables (Common Patterns)
```env
# Database
DB_ROOT_PASSWORD=
DB_USER=
DB_PASSWORD=
DB_NAME=AppDb

# API/JWT
JWT_SECRET=

# S3 / B2 (S3-compatible)
S3_BUCKET_NAME=
S3_ACCESS_KEY=
S3_SECRET_KEY=
S3_REGION=
S3_ENDPOINT=

# Backblaze B2 (when used)
B2_APPLICATION_KEY_ID=
B2_APPLICATION_KEY=
B2_BUCKET_ID=
```
- Example connection string:
```env
ConnectionStrings__DefaultConnection=Server=db;Database=${DB_NAME};User=${DB_USER};Password=${DB_PASSWORD};Port=3306;
```
- Rule: Never log or commit secrets.

## Agent Behavior Model (Machine-Readable Summary)
- Information-gathering:
  - Targeted, minimal; stop once sufficient; confirm signatures before edits
- Task management:
  - Use a short Investigate/Triage task for non-trivial work
  - Keep one task IN_PROGRESS; batch state updates
  - Create follow-up tasks as per Task Creation Policy
- Edits:
  - Conservative, minimal; respect repository structure and conventions
- Efficiency:
  - Prefer smallest set of high-signal actions and validations; ask if blocked
- Communication:
  - Summarize intentions and next steps; ask before risky actions
  - Create tasks for identified follow-up work

## Hosting & Cost Optimization
- Hosting: cloud VM providers (e.g., AWS EC2); consider Vultr/Hetzner/OVH for cost savings
- Region: Prefer AU to serve Brisbane clients
- CDN: Use when cost/performance beneficial; S3-compatible endpoints common
- Avoid expensive operations; reuse caches/artifacts; prune unused images/containers

## Agent Compatibility Notes
- Cline:
  - Follow this doc; propose minimal command sets; show commands and expected outputs
  - Create tasks for follow-up work as per Task Creation Policy
- Roo Code:
  - Short investigations; confirm signatures; tight edits; ask before push/deploy/package changes
  - Generate tasks for identified next steps
- Augment Code:
  - Use codebase context tools judiciously
  - When showing code, use <augment_code_snippet> tags
  - Follow tasklist triggers and minimal tool-call ethos
  - Create follow-up tasks when work identifies future needs

## Ready-to-Use Command Snippets

- Frontend (dev) with OrbStack:
```bash
docker compose -f deploy/docker-compose.dev.yml up -d frontend
curl -s -o /dev/null -w "%{http_code}\n" http://localhost:8080/index.html
```

- Health checks:
```bash
curl -s http://localhost:8080/health
```

- .NET backend quick build:
```bash
cd path/to/Your.Api
dotnet restore && dotnet build -c Release
```

- Python with uv:
```bash
uv python install 3.11
uv venv
uv pip install -r requirements.txt   # or: uv add <pkg>
uv run pytest -q
```

- MariaDB connectivity:
```bash
mysqladmin ping -h 127.0.0.1 -u root -p"${DB_ROOT_PASSWORD}"
```

---

### Tags
#agents #preferences #dev-tools #workflow #ci-cd #security #s3 #b2 #nginx #docker #orbstack #dotnet #mariadb #testing #cost-optimisation #obsidian #cline #roocode #augmentcode #python #uv #pip #mermaid #svg #diagrams #visual-aids #tasks #dataview

---
