/**
 * Modern Card Carousel Component for Yendor Cats
 * Features:
 * - Card-based layout with multiple images visible
 * - Horizontal scrolling
 * - Metadata display on cards (cat name, age, date)
 * - Click to expand to fullscreen with detailed metadata
 * - Touch swipe support for mobile
 * - Auto-refresh from Backblaze storage
 */

class ModernCardCarousel {
    /**
     * Initialize a new modern card carousel
     * @param {HTMLElement} element - The carousel element
     * @param {Object} options - Carousel options
     */
    constructor(element, options = {}) {
        // DOM Elements
        this.carousel = element;
        this.container = this.carousel.querySelector('.carousel-container');
        this.track = this.carousel.querySelector('.carousel-track');
        this.prevButton = this.carousel.querySelector('.carousel-nav.prev');
        this.nextButton = this.carousel.querySelector('.carousel-nav.next');
        // Legacy dot indicators container (we'll repurpose to numeric)
        this.indicators = this.carousel.querySelector('.carousel-indicators');

        // Get category from data attribute
        this.category = this.carousel.dataset.category || 'gallery';

        // Default Options
        this.options = {
            autoplay: false, // Disabled for card carousel
            cardsVisible: {
                desktop: 4,
                tablet: 2,
                mobile: 1
            },
            cardSpacing: 20,
            scrollDistance: 1, // Number of cards to scroll
            infinite: true,
            showMetadata: true,
            apiEndpoint: '/api/PublicGallery/category',
            fallbackEndpoint: '/api/v2/gallery',
            ...options
        };

        // Default sort (Newest by date)
        this.sort = { orderBy: 'date', descending: true };

        // State
        this.currentIndex = 0;
        this.cards = [];
        this.cardWidth = 0;
        this.visibleCards = this.getVisibleCards();
        this.isTransitioning = false;
        this.touchStartX = 0;
        this.touchEndX = 0;
        this.images = [];
        this.lightboxIndex = 0;

        // Create numeric counter element (top-right)
        this.counterEl = document.createElement('div');
        this.counterEl.className = 'carousel-counter';
        // Append inside the main carousel element so it overlays correctly
        this.carousel.appendChild(this.counterEl);

        console.log(`🎠 Initializing ModernCardCarousel for category: ${this.category}`);

        // Initialize
        this.init();
    }

    /**
     * Initialize the carousel
     */
    async init() {
        // Set up event listeners
        this.setupEventListeners();

        // Load images from API
        await this.loadImages();

        // Apply default sort and render
        this.applySort();

        // Create cards
        this.createCards();

        // Setup carousel layout
        this.setupLayout();

        // Create lightbox if it doesn't exist
        this.createLightbox();

        // Bind external controls
        this.bindSortButtons();
        this.setupViewAllButton();

        console.log(`✅ ModernCardCarousel initialized for ${this.category} with ${this.cards.length} cards`);
    }
    /**
     * Apply current sort to this.images
     */
    applySort() {
        if (!Array.isArray(this.images) || this.images.length === 0) return;
        const { orderBy, descending } = this.sort || { orderBy: 'date', descending: true };
        const dir = descending ? -1 : 1;
        const getName = (img) => this.extractCatName(img).toLowerCase();
        const getDateVal = (img) => {
            const raw = this.extractDate(img);
            const t = raw ? Date.parse(raw) : NaN;
            return isNaN(t) ? 0 : t;
        };
        const getAgeVal = (img) => {
            // Try prefer metadata numeric
            const mAge = img.metadata?.age || img.age || img.ageAtPhoto || '';
            if (typeof mAge === 'number') return mAge;
            if (!mAge || typeof mAge !== 'string') return 0;
            const s = mAge.toLowerCase();
            // Extract first number
            const numMatch = s.match(/(\d+(?:\.\d+)?)/);
            const num = numMatch ? parseFloat(numMatch[1]) : 0;
            if (s.includes('year')) return num * 365;
            if (s.includes('month')) return num * 30;
            if (s.includes('week')) return num * 7;
            if (s.includes('day')) return num;
            return num;
        };
        this.images.sort((a, b) => {
            let av, bv;
            switch ((orderBy || '').toLowerCase()) {
                case 'name':
                    av = getName(a); bv = getName(b);
                    return av < bv ? -1 * dir : av > bv ? 1 * dir : 0;
                case 'age':
                    av = getAgeVal(a); bv = getAgeVal(b);
                    return av === bv ? 0 : (av - bv) * dir;
                case 'date':
                case 'datetaken':
                default:
                    av = getDateVal(a); bv = getDateVal(b);
                    return av === bv ? 0 : (av - bv) * dir;
            }
        });
    }

    /**
     * Bind sort buttons for this category
     */
    bindSortButtons() {
        const selector = `.sort-button[data-category="${this.category}"]`;
        const buttons = document.querySelectorAll(selector);
        if (!buttons || buttons.length === 0) return;
        buttons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const orderBy = btn.dataset.orderBy || 'date';
                const descending = btn.dataset.descending === 'true';
                // Update active state within its group
                const group = btn.closest('.sort-buttons');
                if (group) group.querySelectorAll('.sort-button').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                // Update sort state and re-render
                this.sort = { orderBy, descending };
                this.applySort();
                this.createCards();
                this.setupLayout();
            }, { passive: false });
        });
    }

    /**
     * Setup the View All button to show a thumbnail lightbox overlay
     */
    setupViewAllButton() {
        const btn = document.querySelector(`.view-all-button[data-category="${this.category}"]`);
        if (!btn) return;
        const popup = document.querySelector('.thumbnail-gallery-popup');
        const grid = popup?.querySelector('.thumbnail-grid');
        const title = popup?.querySelector('.thumbnail-gallery-title');
        const closeBtn = popup?.querySelector('.thumbnail-gallery-close');
        if (!popup || !grid) return;
        const open = () => {
            // Populate grid
            grid.innerHTML = '';
            this.images.forEach((img, idx) => {
                const thumb = document.createElement('div');
                thumb.className = 'thumbnail-item';
                thumb.innerHTML = `<img src="${img.imageUrl || img.publicUrl}" alt="${this.extractCatName(img)}" loading="lazy"/>`;
                thumb.addEventListener('click', () => {
                    popup.classList.remove('active');
                    document.body.style.overflow = '';
                    this.openLightbox(idx);
                });
                grid.appendChild(thumb);
            });
            if (title) title.textContent = `${this.category.charAt(0).toUpperCase()}${this.category.slice(1)} — All Photos`;
            popup.classList.add('active');
            document.body.style.overflow = 'hidden';
        };
        const close = () => {
            popup.classList.remove('active');
            document.body.style.overflow = '';
        };
        btn.addEventListener('click', (e) => { e.preventDefault(); open(); });
        if (closeBtn) closeBtn.addEventListener('click', close);
        // Close when clicking outside grid container
        popup.addEventListener('click', (e) => {
            const container = popup.querySelector('.thumbnail-gallery-container');
            if (e.target === popup || (container && e.target === container)) {
                close();
            }
        });
        // Esc key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && popup.classList.contains('active')) close();
        });
    }


    /**
     * Load images from API with fallback
     */
    async loadImages() {
        try {
            console.log(`📡 Loading images for category: ${this.category}`);

            // Try new V2 API first
            let response = await fetch(`${this.options.apiEndpoint}/${this.category}?page=1&pageSize=50`);
            let data;

            if (response.ok) {
                data = await response.json();
                // Handle V2 API response format
                this.images = data.items || data.images || [];
                console.log(`📸 Loaded ${this.images.length} images from V2 API for ${this.category}`);
            } else {
                console.warn(`V2 API failed, trying fallback for ${this.category}`);
                // Fallback to old API
                response = await fetch(`${this.options.fallbackEndpoint}/${this.category}`);

                if (!response.ok) {
                    throw new Error(`Both APIs failed! V2: ${response.status}`);
                }

                data = await response.json();
                // Handle legacy API response format
                this.images = Array.isArray(data) ? data : (data.images || []);
                console.log(`📸 Loaded ${this.images.length} images from fallback API for ${this.category}`);
            }

            // Remove loading placeholder
            const loadingPlaceholder = this.track.querySelector('.loading-placeholder');
            if (loadingPlaceholder) {
                loadingPlaceholder.remove();
            }

        } catch (error) {
            console.error(`❌ Error loading images for ${this.category}:`, error);
            this.showError('Failed to load images. Please try again later.');
        }
    }

    /**
     * Create card elements from images
     */
    createCards() {
        // Clear existing
        this.cards = [];
        if (this.track) this.track.innerHTML = '';

        this.images.forEach((image, index) => {
            const card = this.createCard(image, index);
            this.cards.push(card);
            this.track.appendChild(card);
        });

        // Reset index when cards change
        this.currentIndex = 0;
        this.updateCardPositions();
        this.updateNavigation();
        this.updateIndicators();

        console.log(`🃏 Created ${this.cards.length} cards`);
    }

    /**
     * Create a single card element
     * @param {Object} image - Image data
     * @param {number} index - Image index
     * @returns {HTMLElement} Card element
     */
    createCard(image, index) {
        const card = document.createElement('div');
        card.className = 'cat-card';
        card.dataset.imageId = image.id || index;
        card.dataset.index = index;

        // Extract metadata with improved logic
        const catName = this.extractCatName(image);
        const age = this.extractAge(image);
        const dateTaken = this.extractDate(image);
        const formattedDate = this.formatDate(dateTaken);
        const description = this.extractDescription(image);

        card.innerHTML = `
            <div class="card-image">
                <img src="${image.imageUrl || image.publicUrl}" alt="${catName}" loading="lazy">
                <div class="card-overlay">
                    <h3 class="cat-name">${catName}</h3>
                    ${age ? `<span class="cat-age">${age}</span>` : ''}
                </div>
                <div class="card-hover-overlay">
                    <div class="card-actions">
                        <button class="card-expand-btn" aria-label="View full size">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="15 3 21 3 21 9"></polyline>
                                <polyline points="9 21 3 21 3 15"></polyline>
                                <line x1="21" y1="3" x2="14" y2="10"></line>
                                <line x1="3" y1="21" x2="10" y2="14"></line>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <span class="photo-date">${formattedDate}</span>
                ${description ? `<p class="photo-description">${description}</p>` : ''}
            </div>
        `;

        // Add click event to open lightbox
        card.addEventListener('click', () => {
            this.openLightbox(index);
        });

        return card;
    }

    /**
     * Extract cat name from image data
     * @param {Object} image - Image data
     * @returns {string} Cat name
     */
    extractCatName(image) {
        // Try multiple sources for cat name
        if (image.catName && image.catName !== this.category) {
            return image.catName;
        }
        if (image.title && image.title !== this.category) {
            return image.title;
        }
        if (image.metadata?.catName && image.metadata.catName !== this.category) {
            return image.metadata.catName;
        }
        if (image.fileName) {
            // Extract name from filename (remove extension and common patterns)
            let name = image.fileName.replace(/\.(jpg|jpeg|png|gif|webp)$/i, '');
            // Remove date patterns like "2024-01-01" or "01-01-2024"
            name = name.replace(/\d{4}-\d{1,2}-\d{1,2}/g, '');
            name = name.replace(/\d{1,2}-\d{1,2}-\d{4}/g, '');
            // Remove number suffixes like "-1", "-2"
            name = name.replace(/-\d+$/g, '');
            // Clean up and capitalize
            name = name.replace(/[-_]/g, ' ').trim();
            if (name && name !== this.category) {
                return name.split(' ').map(word =>
                    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                ).join(' ');
            }
        }
        return `Beautiful ${this.category.slice(0, -1)}`; // Remove 's' from category name
    }

    /**
     * Extract age information from image data
     * @param {Object} image - Image data
     * @returns {string} Age information
     */
    extractAge(image) {
        if (image.ageAtPhoto) return image.ageAtPhoto;
        if (image.metadata?.age) return image.metadata.age;
        if (image.age) return image.age;
        return '';
    }

    /**
     * Extract date from image data
     * @param {Object} image - Image data
     * @returns {string} Date string
     */
    extractDate(image) {
        return image.dateTaken || image.dateCreated || image.metadata?.dateTaken || image.dateUploaded || '';
    }

    /**
     * Extract description from image data
     * @param {Object} image - Image data
     * @returns {string} Description
     */
    extractDescription(image) {
        if (image.description && image.description.length > 0) {
            return image.description.length > 50 ?
                image.description.substring(0, 50) + '...' :
                image.description;
        }
        if (image.metadata?.description) {
            return image.metadata.description.length > 50 ?
                image.metadata.description.substring(0, 50) + '...' :
                image.metadata.description;
        }
        return '';
    }

    /**
     * Format date for display
     * @param {string} dateString - Date string
     * @returns {string} Formatted date
     */
    formatDate(dateString) {
        if (!dateString) return '';

        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        } catch (error) {
            return dateString;
        }
    }

    /**
     * Setup carousel layout and dimensions
     */
    setupLayout() {
        // Calculate card width based on visible cards
        this.updateLayout();

        // Position cards
        this.updateCardPositions();

        // Update navigation state
        this.updateNavigation();

        // Update indicators
        this.updateIndicators();
    }

    /**
     * Update layout based on screen size
     */
    updateLayout() {
        this.visibleCards = this.getVisibleCards();

        const containerWidth = this.container.offsetWidth;
        const totalSpacing = (this.visibleCards - 1) * this.options.cardSpacing;
        this.cardWidth = (containerWidth - totalSpacing) / this.visibleCards;

        // Set card widths
        this.cards.forEach(card => {
            card.style.width = `${this.cardWidth}px`;
            card.style.marginRight = `${this.options.cardSpacing}px`;
        });

        // Remove margin from last card in view
        if (this.cards.length > 0) {
            const lastVisibleIndex = Math.min(this.currentIndex + this.visibleCards - 1, this.cards.length - 1);
            if (this.cards[lastVisibleIndex]) {
                this.cards[lastVisibleIndex].style.marginRight = '0px';
            }
        }

        console.log(`📐 Layout updated: ${this.visibleCards} cards visible, card width: ${this.cardWidth}px`);
    }

    /**
     * Get number of visible cards based on screen size
     * @returns {number} Number of visible cards
     */
    getVisibleCards() {
        const width = window.innerWidth;

        if (width >= 1200) {
            return this.options.cardsVisible.desktop;
        } else if (width >= 768) {
            return this.options.cardsVisible.tablet;
        } else {
            return this.options.cardsVisible.mobile;
        }
    }

    /**
     * Update card positions based on current index
     */
    updateCardPositions() {
        if (this.cards.length === 0) return;

        const translateX = -this.currentIndex * (this.cardWidth + this.options.cardSpacing);

        this.track.style.transform = `translateX(${translateX}px)`;
        this.track.style.transition = this.isTransitioning ? 'transform 0.3s ease' : 'none';

        console.log(`🎯 Moved to index ${this.currentIndex}, translateX: ${translateX}px`);
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Navigation buttons
        this.prevButton.addEventListener('click', () => this.prev());
        this.nextButton.addEventListener('click', () => this.next());

        // Touch events
        this.track.addEventListener('touchstart', e => this.handleTouchStart(e), { passive: true });
        this.track.addEventListener('touchmove', e => this.handleTouchMove(e), { passive: true });
        this.track.addEventListener('touchend', () => this.handleTouchEnd());

        // Horizontal wheel scrolling (trackpads/mice)
        this.container.addEventListener('wheel', (e) => this.handleWheel(e), { passive: false });

        // Window resize
        window.addEventListener('resize', () => this.handleResize());

        // Keyboard navigation
        document.addEventListener('keydown', e => this.handleKeyDown(e));
    }
    /**
     * Handle wheel horizontal scrolling
     */
    handleWheel(e) {
        // If horizontal scroll is intended, prevent page scroll and move carousel
        const deltaX = e.deltaX || 0;
        const deltaY = e.deltaY || 0;
        // Treat shift+wheel vertical as horizontal as well
        const effectiveDeltaX = Math.abs(deltaX) > Math.abs(deltaY) || e.shiftKey ? deltaX || deltaY : 0;
        if (effectiveDeltaX === 0) return;
        e.preventDefault();
        if (effectiveDeltaX > 0) {
            this.next();
        } else {
            this.prev();
        }
    }


    /**
     * Handle window resize
     */
    handleResize() {
        clearTimeout(this.resizeTimeout);
        this.resizeTimeout = setTimeout(() => {
            this.updateLayout();
            this.updateCardPositions();
            this.updateNavigation();
            this.updateIndicators();
        }, 100);
    }

    /**
     * Handle touch start
     * @param {TouchEvent} e - Touch event
     */
    handleTouchStart(e) {
        this.touchStartX = e.touches[0].clientX;
    }

    /**
     * Handle touch move
     * @param {TouchEvent} e - Touch event
     */
    handleTouchMove(e) {
        this.touchEndX = e.touches[0].clientX;
    }

    /**
     * Handle touch end
     */
    handleTouchEnd() {
        const difference = this.touchStartX - this.touchEndX;
        const threshold = 50;

        if (difference > threshold) {
            this.next();
        } else if (difference < -threshold) {
            this.prev();
        }
    }

    /**
     * Handle keyboard navigation
     * @param {KeyboardEvent} e - Keyboard event
     */
    handleKeyDown(e) {
        if (!this.carousel.matches(':hover')) return;

        switch (e.key) {
            case 'ArrowLeft':
                e.preventDefault();
                this.prev();
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.next();
                break;
        }
    }

    /**
     * Go to previous cards
     */
    prev() {
        if (this.isTransitioning) return;

        const newIndex = Math.max(0, this.currentIndex - this.options.scrollDistance);
        this.goToIndex(newIndex);
    }

    /**
     * Go to next cards
     */
    next() {
        if (this.isTransitioning) return;

        const maxIndex = Math.max(0, this.cards.length - this.visibleCards);
        const newIndex = Math.min(maxIndex, this.currentIndex + this.options.scrollDistance);
        this.goToIndex(newIndex);
    }

    /**
     * Go to specific index
     * @param {number} index - Target index
     */
    goToIndex(index) {
        if (this.isTransitioning || index === this.currentIndex) return;

        this.isTransitioning = true;
        this.currentIndex = index;

        this.updateCardPositions();
        this.updateNavigation();
        this.updateIndicators();

        setTimeout(() => {
            this.isTransitioning = false;
        }, 300);
    }

    /**
     * Update navigation button states
     */
    updateNavigation() {
        const maxIndex = Math.max(0, this.cards.length - this.visibleCards);

        this.prevButton.disabled = this.currentIndex <= 0;
        this.nextButton.disabled = this.currentIndex >= maxIndex;

        this.prevButton.style.opacity = this.prevButton.disabled ? '0.5' : '1';
        this.nextButton.style.opacity = this.nextButton.disabled ? '0.5' : '1';
    }

    /**
     * Update indicators (numeric counter at top-right)
     */
    updateIndicators() {
        const total = this.cards.length;
        if (!this.counterEl) return;
        if (total === 0) {
            this.counterEl.textContent = '';
            this.counterEl.setAttribute('aria-hidden', 'true');
            return;
        }
        const start = this.currentIndex + 1;
        const end = Math.min(this.currentIndex + this.visibleCards, total);
        this.counterEl.textContent = `${start}-${end} / ${total}`;
        this.counterEl.setAttribute('aria-label', `Showing items ${start} to ${end} of ${total}`);
    }

    /**
     * Create lightbox for full-size image viewing
     */
    createLightbox() {
        // Check if lightbox already exists
        if (document.querySelector('.cat-lightbox')) return;

        const lightbox = document.createElement('div');
        lightbox.className = 'cat-lightbox';

        lightbox.innerHTML = `
            <div class="lightbox-backdrop"></div>
            <div class="lightbox-content">
                <div class="lightbox-header">
                    <h2 class="lightbox-title">Cat Name</h2>
                    <button class="lightbox-close" aria-label="Close lightbox">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
                <div class="lightbox-body">
                    <div class="lightbox-image-container">
                        <img class="lightbox-image" src="" alt="">
                        <button class="lightbox-nav-btn prev" aria-label="Previous image">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="15 18 9 12 15 6"></polyline>
                            </svg>
                        </button>
                        <button class="lightbox-nav-btn next" aria-label="Next image">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="9 18 15 12 9 6"></polyline>
                            </svg>
                        </button>
                    </div>
                    <div class="lightbox-metadata">
                        <div class="metadata-item">
                            <label>Cat Name:</label>
                            <span class="metadata-cat-name">-</span>
                        </div>
                        <div class="metadata-item">
                            <label>Age:</label>
                            <span class="metadata-age">-</span>
                        </div>
                        <div class="metadata-item">
                            <label>Date Taken:</label>
                            <span class="metadata-date">-</span>
                        </div>
                        <div class="metadata-item">
                            <label>Category:</label>
                            <span class="metadata-category">-</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(lightbox);

        // Setup lightbox event listeners
        this.setupLightboxListeners(lightbox);
    }

    /**
     * Setup lightbox event listeners
     * @param {HTMLElement} lightbox - Lightbox element
     */
    setupLightboxListeners(lightbox) {
        const closeBtn = lightbox.querySelector('.lightbox-close');
        const backdrop = lightbox.querySelector('.lightbox-backdrop');
        const prevBtn = lightbox.querySelector('.lightbox-nav-btn.prev');
        const nextBtn = lightbox.querySelector('.lightbox-nav-btn.next');

        closeBtn.addEventListener('click', () => this.closeLightbox());
        backdrop.addEventListener('click', () => this.closeLightbox());
        prevBtn.addEventListener('click', () => this.lightboxPrev());
        nextBtn.addEventListener('click', () => this.lightboxNext());

        // Keyboard events
        document.addEventListener('keydown', (e) => {
            if (!lightbox.classList.contains('active')) return;

            switch (e.key) {
                case 'Escape':
                    this.closeLightbox();
                    break;
                case 'ArrowLeft':
                    this.lightboxPrev();
                    break;
                case 'ArrowRight':
                    this.lightboxNext();
                    break;
            }
        });
    }

    /**
     * Open lightbox with specific image
     * @param {number} index - Image index
     */
    openLightbox(index) {
        this.lightboxIndex = index;
        const lightbox = document.querySelector('.cat-lightbox');
        const image = this.images[index];

        if (!image || !lightbox) return;

        // Update lightbox content
        this.updateLightboxContent(image);

        // Show lightbox
        lightbox.classList.add('active');
        document.body.style.overflow = 'hidden';

        console.log(`🔍 Opened lightbox for image ${index + 1}/${this.images.length}`);
    }

    /**
     * Update lightbox content
     * @param {Object} image - Image data
     */
    updateLightboxContent(image) {
        const lightbox = document.querySelector('.cat-lightbox');

        const title = lightbox.querySelector('.lightbox-title');
        const img = lightbox.querySelector('.lightbox-image');
        const catName = lightbox.querySelector('.metadata-cat-name');
        const age = lightbox.querySelector('.metadata-age');
        const date = lightbox.querySelector('.metadata-date');
        const category = lightbox.querySelector('.metadata-category');

        const catNameText = image.metadata?.catName || image.fileName?.split('.')[0] || 'Unknown';

        title.textContent = catNameText;
        img.src = image.imageUrl;
        img.alt = catNameText;
        catName.textContent = catNameText;
        age.textContent = image.metadata?.age || 'Unknown';
        date.textContent = this.formatDate(image.metadata?.dateTaken || image.dateCreated) || 'Unknown';
        category.textContent = this.category.charAt(0).toUpperCase() + this.category.slice(1);
    }

    /**
     * Close lightbox
     */
    closeLightbox() {
        const lightbox = document.querySelector('.cat-lightbox');
        lightbox.classList.remove('active');
        document.body.style.overflow = '';
    }

    /**
     * Go to previous image in lightbox
     */
    lightboxPrev() {
        if (this.lightboxIndex > 0) {
            this.lightboxIndex--;
            this.updateLightboxContent(this.images[this.lightboxIndex]);
        }
    }

    /**
     * Go to next image in lightbox
     */
    lightboxNext() {
        if (this.lightboxIndex < this.images.length - 1) {
            this.lightboxIndex++;
            this.updateLightboxContent(this.images[this.lightboxIndex]);
        }
    }

    /**
     * Show error message
     * @param {string} message - Error message
     */
    showError(message) {
        this.track.innerHTML = `
            <div class="error-placeholder">
                <div class="error-icon">❌</div>
                <p>${message}</p>
                <button class="retry-button" onclick="location.reload()">Retry</button>
            </div>
        `;
    }

    /**
     * Refresh carousel data from API
     */
    async refresh() {
        console.log(`🔄 Refreshing carousel data for ${this.category}`);

        // Show loading state
        this.track.innerHTML = `
            <div class="loading-placeholder">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                </div>
                <p>Refreshing ${this.category}...</p>
                <small>Checking for new images</small>
            </div>
        `;

        // Reset state
        this.currentIndex = 0;
        this.cards = [];
        this.images = [];

        // Reload images and recreate carousel
        await this.loadImages();
        this.applySort();
        this.createCards();
        this.setupLayout();

        console.log(`✅ Carousel refreshed for ${this.category}`);
    }
}

// Export for use in other scripts
if (typeof module !== 'undefined') {
    module.exports = { ModernCardCarousel };
}
